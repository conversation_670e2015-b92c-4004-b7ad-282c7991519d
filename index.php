<?php
require_once 'functions.php';

// Handle Add Task
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	if (isset($_POST["task-name"])) {
		$taskName = trim($_POST["task-name"]);
		if (!empty($taskName)) {
			addTask($taskName);
		}
	}

	// Handle Task Completion Toggle
	if (isset($_POST["toggle-task-id"])) {
		$taskId = $_POST["toggle-task-id"];
		$tasks = getAllTasks();
		foreach ($tasks as $task) {
			if ($task['id'] === $taskId) {
				markTaskAsCompleted($taskId, !$task["completed"]);
				break;
			}
		}
	}

	// Handle Task Deletion
	if (isset($_POST["delete-task-id"])) {
		deleteTask($_POST["delete-task-id"]);
	}

	// Handle Email Subscription 
	// Handle Email Subscription
if (isset($_POST["email"])) {
	$email = trim($_POST["email"]);
	if (!empty($email)) {
		if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
			if (subscribeEmail($email)) {
				echo "<script>alert(' Verification email sent to $email');</script>";
			} else {
				echo "<script>alert(' Already subscribed or pending verification.');</script>";
			}
		} else {
			echo "<script>alert(' Invalid email address.');</script>";
		}
	}
}

}

// Get tasks to display
$tasks = getAllTasks();
?>

<!DOCTYPE html>
<html>
<head>
	<title>Task Scheduler</title>
	<style>
		body {
			font-family: 'Segoe UI', Arial, sans-serif;
			background: #f7f7f9;
			margin: 0;
			padding: 0;
		}
		.container {
			max-width: 480px;
			margin: 40px auto;
			background: #fff;
			border-radius: 8px;
			box-shadow: 0 2px 12px rgba(0,0,0,0.08);
			padding: 32px 28px 24px 28px;
		}
		h2 {
			margin-top: 0;
			color: #2d3e50;
			font-weight: 600;
			letter-spacing: 0.5px;
		}
		form {
			margin-bottom: 24px;
		}
		input[type="text"], input[type="email"] {
			width: 70%;
			padding: 8px 10px;
			border: 1px solid #cfd8dc;
			border-radius: 4px;
			font-size: 15px;
			margin-right: 8px;
			box-sizing: border-box;
		}
		button {
			background: #1976d2;
			color: #fff;
			border: none;
			padding: 8px 18px;
			border-radius: 4px;
			font-size: 15px;
			cursor: pointer;
			transition: background 0.2s;
		}
		button:hover {
			background: #125ea7;
		}
		ul.tasks-list {
			list-style: none;
			padding: 0;
			margin: 0 0 24px 0;
		}
		li.task-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			background: #f1f5fb;
			border-radius: 4px;
			padding: 10px 14px;
			margin-bottom: 10px;
			font-size: 16px;
		}
		.task-info {
			display: flex;
			align-items: center;
			flex: 1;
			gap: 8px;
		}
		.task-info form {
			margin: 0;
			padding: 0;
			display: flex;
			align-items: center;
		}
		.task-info input[type="checkbox"] {
			margin: 0 8px 0 0;
			transform: scale(1.2);
			vertical-align: middle;
		}
		.task-info label {
			cursor: pointer;
			margin: 0;
		}
		.completed label {
			text-decoration: line-through;
			color: #9e9e9e;
		}
		.delete-task {
			background: #e53935;
			margin-left: 10px;
			padding: 6px 12px;
			font-size: 14px;
		}
		.delete-task:hover {
			background: #b71c1c;
		}
		@media (max-width: 600px) {
			.container {
				padding: 16px 6px;
			}
			input[type="text"], input[type="email"] {
				width: 100%;
				margin-bottom: 8px;
			}
			button {
				width: 100%;
				margin-bottom: 8px;
			}
			li.task-item {
				flex-direction: column;
				align-items: flex-start;
			}
			.task-info {
				width: 100%;
			}
		}
	</style>
</head>
<body>
	<div class="container">
		<h2>Add New Task</h2>
		<form method="POST" action="">
			<input type="text" name="task-name" id="task-name" placeholder="Enter new task" required>
			<button type="submit" id="add-task">Add Task</button>
		</form>

		<h2>Task List</h2>
		<ul class="tasks-list">
			<?php foreach ($tasks as $task): ?>
				<li class="task-item <?php echo $task['completed'] ? 'completed' : ''; ?>">
					<div class="task-info">
						<form method="POST" style="display:flex; align-items:center; margin:0; padding:0;">
							<input type="hidden" name="toggle-task-id" value="<?php echo $task['id']; ?>">
							<input type="checkbox" class="task-status" <?php echo $task['completed'] ? 'checked' : ''; ?> onchange="this.form.submit()">
						</form>
						<label><?php echo htmlspecialchars($task['name']); ?></label>
					</div>
					<form method="POST" style="display:inline;">
						<input type="hidden" name="delete-task-id" value="<?php echo $task['id']; ?>">
						<button class="delete-task">Delete</button>
					</form>
				</li>
			<?php endforeach; ?>
		</ul>

		<h2>Subscribe to Email Reminders</h2>
		<form method="POST" action="">
			<input type="email" name="email" placeholder="Enter your email" required />
			<button type="submit" id="submit-email">Subscribe</button>
		</form>
	</div>
</body>
</html>
