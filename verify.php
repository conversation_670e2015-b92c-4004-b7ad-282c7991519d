<?php
require_once 'functions.php';

$message = '';

if (isset($_GET['email']) && isset($_GET['code'])) {
    $email = $_GET['email'];
    $code = $_GET['code'];

    // Call the verification function
    if (verifySubscription($email, $code)) {
        $message = " Subscription verified successfully!";
    } else {
        $message = " Verification failed. Invalid or expired code.";
    }
} else {
    $message = " Invalid verification link.";
}
?>

<!DOCTYPE html>
<html>
<head>
	<title>Verify Subscription</title>
	<style>
		body { font-family: Arial, sans-serif; padding: 30px; }
		h2 { color: #333; }
		p { font-size: 18px; margin-top: 20px; }
	</style>
</head>
<body>
	<h2 id="verification-heading">Subscription Verification</h2>
	<p><?= htmlspecialchars($message) ?></p>
</body>
</html>
