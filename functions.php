<?php

/**
 * Adds a new task to the task list
 * 
 * @param string $task_name The name of the task to add.
 * @return bool True on success, false on failure.
 */
function addTask(string $task_name): bool {
    $file = __DIR__ . '/tasks.txt';
    $tasks = [];

    if (file_exists($file)) {
        $tasks = json_decode(file_get_contents($file), true) ?? [];
    }

    // Avoid duplicate (case-insensitive)
    foreach ($tasks as $task) {
        if (strtolower($task['name']) === strtolower($task_name)) {
            return false; // Duplicate task
        }
    }

    $newTask = [
        'id' => uniqid(),
        'name' => $task_name,
        'completed' => false
    ];

    $tasks[] = $newTask;
    return file_put_contents($file, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
}

/**
 * Retrieves all tasks from the tasks.txt file
 * 
 * @return array Array of tasks. -- Format [ id, name, completed ]
 */
function getAllTasks(): array {
    $file = __DIR__ . '/tasks.txt';

    if (!file_exists($file)) {
        return [];
    }

    $tasks = json_decode(file_get_contents($file), true);
    return $tasks ?? [];
}

/**
 * Marks a task as completed or uncompleted
 * 
 * @param string  $task_id The ID of the task to mark.
 * @param bool $is_completed True to mark as completed, false to mark as uncompleted.
 * @return bool True on success, false on failure
 */
function markTaskAsCompleted(string $task_id, bool $is_completed): bool {
    $file = __DIR__ . '/tasks.txt';

    if (!file_exists($file)) return false;

    $tasks = json_decode(file_get_contents($file), true);
    $found = false;

    foreach ($tasks as &$task) {
        if ($task['id'] === $task_id) {
            $task['completed'] = $is_completed;
            $found = true;
            break;
        }
    }

    if (!$found) return false;

    return file_put_contents($file, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
}

/**
 * Deletes a task from the task list
 * 
 * @param string $task_id The ID of the task to delete.
 * @return bool True on success, false on failure.
 */
function deleteTask(string $task_id): bool {
    $file = __DIR__ . '/tasks.txt';

    if (!file_exists($file)) return false;

    $tasks = json_decode(file_get_contents($file), true);
    $originalCount = count($tasks);

    // Filter out the task with matching ID
    $tasks = array_filter($tasks, fn($task) => $task['id'] !== $task_id);

    if (count($tasks) === $originalCount) return false; // No deletion happened

    return file_put_contents($file, json_encode(array_values($tasks), JSON_PRETTY_PRINT)) !== false;
}

/**
 * Generates a 6-digit verification code
 * 
 * @return string The generated verification code.
 */
function generateVerificationCode(): string {
    return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Subscribe an email address to task notifications.
 *
 * Generates a verification code, stores the pending subscription,
 * and sends a verification email to the subscriber.
 *
 * @param string $email The email address to subscribe.
 * @return bool True if verification email sent successfully, false otherwise.
 */
function subscribeEmail(string $email): bool {
    $file = __DIR__ . '/pending_subscriptions.txt';
    $pending = [];

    if (file_exists($file)) {
        $pending = json_decode(file_get_contents($file), true) ?? [];
    }

    // Already pending or already subscribed
    if (isset($pending[$email])) return false;

    $subscribers = json_decode(file_get_contents(__DIR__ . '/subscribers.txt'), true) ?? [];
    if (in_array($email, $subscribers)) return false;

    $code = generateVerificationCode();
    $pending[$email] = [
        "code" => $code,
        "timestamp" => time()
    ];

    // Save to pending
    file_put_contents($file, json_encode($pending, JSON_PRETTY_PRINT));

    // Create verification link
    if (isset($_SERVER['HTTP_HOST'])) {
        $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    } else {
        // Fallback for cron jobs - you may need to adjust this URL
        $base_url = "http://localhost" . dirname(__FILE__);
    }
    $verification_link = $base_url . "/verify.php?email=" . urlencode($email) . "&code=$code";

    // Send email
    $subject = "Verify subscription to Task Planner";
    $message = '<p>Click the link below to verify your subscription to Task Planner:</p>';
    $message .= '<p><a id="verification-link" href="' . $verification_link . '">Verify Subscription</a></p>';

    $headers  = "MIME-Version: 1.0\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8\r\n";
    $headers .= "From: <EMAIL>\r\n";

    return mail($email, $subject, $message, $headers);
}

/**
 * Verifies an email subscription
 * 
 * @param string $email The email address to verify.
 * @param string $code The verification code.
 * @return bool True on success, false on failure.
 */
function verifySubscription(string $email, string $code): bool {
    $pending_file = __DIR__ . '/pending_subscriptions.txt';
    $subscribers_file = __DIR__ . '/subscribers.txt';

    $pending = json_decode(file_get_contents($pending_file), true) ?? [];
    $subscribers = json_decode(file_get_contents($subscribers_file), true) ?? [];

    if (!isset($pending[$email])) return false;
    if ($pending[$email]['code'] !== $code) return false;

    // Add to verified subscribers
    $subscribers[] = $email;
    $subscribers = array_unique($subscribers);
    file_put_contents($subscribers_file, json_encode($subscribers, JSON_PRETTY_PRINT));

    // Remove from pending
    unset($pending[$email]);
    file_put_contents($pending_file, json_encode($pending, JSON_PRETTY_PRINT));

    return true;
}
/**
 * Unsubscribes an email from task notifications
 * 
 * @param string $email The email address to unsubscribe.
 * @return bool True on success, false on failure.
 */
function unsubscribeEmail(string $email): bool {
    $file = __DIR__ . '/subscribers.txt';
    if (!file_exists($file)) return false;

    $subscribers = json_decode(file_get_contents($file), true) ?? [];
    if (!in_array($email, $subscribers)) return false;

    $updated = array_values(array_filter($subscribers, fn($e) => $e !== $email));
    return file_put_contents($file, json_encode($updated, JSON_PRETTY_PRINT)) !== false;
}

/**
 * Sends task reminders to all subscribers
 */
function sendTaskReminders(): void {
    $subscribers_file = __DIR__ . '/subscribers.txt';
    $subscribers = json_decode(file_get_contents($subscribers_file), true) ?? [];

    $tasks = getAllTasks();
    $pending_tasks = array_filter($tasks, fn($t) => !$t['completed']);

    foreach ($subscribers as $email) {
        sendTaskEmail($email, $pending_tasks);
    }
}


function sendTaskEmail(string $email, array $pending_tasks): bool {
    $subject = "Task Planner - Pending Tasks Reminder";

    $message = "<h2>Pending Tasks Reminder</h2>";
    $message .= "<p>Here are the current pending tasks:</p><ul>";

    foreach ($pending_tasks as $task) {
        $message .= "<li>" . htmlspecialchars($task['name']) . "</li>";
    }

    $message .= "</ul>";

    // For cron jobs, use a fallback URL since $_SERVER might not be available
    if (isset($_SERVER['HTTP_HOST'])) {
        $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    } else {
        // Fallback for cron jobs - you may need to adjust this URL
        $base_url = "http://localhost" . dirname(__FILE__);
    }
    $unsubscribe_link = $base_url . "/unsubscribe.php?email=" . urlencode($email);
    $message .= "<p><a id='unsubscribe-link' href='$unsubscribe_link'>Unsubscribe from notifications</a></p>";

    $headers  = "MIME-Version: 1.0\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8\r\n";
    $headers .= "From: <EMAIL>\r\n";

    return mail($email, $subject, $message, $headers);


}




