<?php
require_once 'functions.php';

$message = "";

if (isset($_GET['email'])) {
    $email = urldecode($_GET['email']);
    if (unsubscribeEmail($email)) {
        $message = " You have been unsubscribed from Task Planner reminders.";
    } else {
        $message = " Unsubscription failed. Email not found or already removed.";
    }
} else {
    $message = " Invalid request. No email provided.";
}
?>

<!DOCTYPE html>
<html>
<head>
	<title>Unsubscribe from Task Planner</title>
	<style>
		body { font-family: Arial, sans-serif; margin: 2rem; }
		.message { font-size: 1.2rem; color: #333; }
	</style>
</head>
<body>
	<h2 id="unsubscription-heading">Unsubscribe from Task Updates</h2>
	<p class="message"><?= htmlspecialchars($message) ?></p>
</body>
</html>
